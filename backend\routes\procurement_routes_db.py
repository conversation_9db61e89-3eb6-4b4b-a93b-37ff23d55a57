from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
import uuid
from functools import wraps

# Import utilities
from utils import (
    token_required, paginate_results, 
    filter_data_by_tenant, check_tenant_access, require_module_access,
    require_role, get_user_accessible_modules
)
from database_manager import db_manager
from services.automated_procurement_service import automated_procurement_service
from services.accounts_payable_service import AccountsPayableService

procurement_bp = Blueprint('procurement', __name__)

# Helper function to generate unique numbers
def generate_request_number():
    """Generate unique purchase request number"""
    today = datetime.now().strftime('%Y%m%d')
    
    # Get count of requests created today
    query = """
        SELECT COUNT(*) as count 
        FROM purchase_requests 
        WHERE request_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'PR-{today}-%',))
    count = result[0]['count'] if result else 0
    
    return f'PR-{today}-{count + 1:04d}'

def generate_po_number():
    """Generate unique purchase order number"""
    today = datetime.now().strftime('%Y%m%d')
    
    query = """
        SELECT COUNT(*) as count 
        FROM purchase_orders 
        WHERE po_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'PO-{today}-%',))
    count = result[0]['count'] if result else 0
    
    return f'PO-{today}-{count + 1:04d}'

def generate_proforma_number():
    """Generate unique proforma invoice number"""
    today = datetime.now().strftime('%Y%m%d')
    
    query = """
        SELECT COUNT(*) as count 
        FROM proforma_invoices 
        WHERE proforma_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'PI-{today}-%',))
    count = result[0]['count'] if result else 0
    
    return f'PI-{today}-{count + 1:04d}'

def generate_delivery_number():
    """Generate unique delivery note number"""
    today = datetime.now().strftime('%Y%m%d')
    
    query = """
        SELECT COUNT(*) as count 
        FROM delivery_notes 
        WHERE delivery_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'DN-{today}-%',))
    count = result[0]['count'] if result else 0
    
    return f'DN-{today}-{count + 1:04d}'

def generate_transfer_number():
    """Generate unique transfer number"""
    today = datetime.now().strftime('%Y%m%d')
    
    query = """
        SELECT COUNT(*) as count 
        FROM inventory_transfers 
        WHERE transfer_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'TR-{today}-%',))
    count = result[0]['count'] if result else 0
    
    return f'TR-{today}-{count + 1:04d}'

def generate_transaction_number():
    """Generate unique transaction number"""
    today = datetime.now().strftime('%Y%m%d')
    
    query = """
        SELECT COUNT(*) as count 
        FROM payment_transactions 
        WHERE transaction_number LIKE ?
    """
    result = db_manager.execute_query(query, (f'TXN-{today}-%',))
    count = result[0]['count'] if result else 0

    return f'TXN-{today}-{count + 1:04d}'

# Dashboard Route
@procurement_bp.route('/api/procurement/dashboard', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_procurement_dashboard():
    """Get procurement dashboard metrics"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Base query conditions for tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all data
            tenant_condition = ""
            tenant_params = ()
        else:
            # Franchise users can only see their own data
            tenant_condition = "WHERE pr.requesting_tenant_id = ?"
            tenant_params = (user_tenant_id,)

        # Get purchase request statistics
        pr_stats_query = f"""
            SELECT
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_requests,
                SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
                SUM(total_estimated_amount) as total_estimated_value
            FROM purchase_requests pr
            {tenant_condition}
        """

        pr_stats = db_manager.execute_query(pr_stats_query, tenant_params)
        pr_data = pr_stats[0] if pr_stats else {
            'total_requests': 0, 'draft_requests': 0, 'pending_requests': 0,
            'approved_requests': 0, 'rejected_requests': 0, 'total_estimated_value': 0
        }

        # Get purchase order statistics
        po_stats_query = f"""
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_orders,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(total_amount) as total_order_value
            FROM purchase_orders po
            {tenant_condition.replace('pr.requesting_tenant_id', 'po.tenant_id') if tenant_condition else ''}
        """

        po_stats = db_manager.execute_query(po_stats_query, tenant_params)
        po_data = po_stats[0] if po_stats else {
            'total_orders': 0, 'draft_orders': 0, 'approved_orders': 0,
            'completed_orders': 0, 'total_order_value': 0
        }

        # Get recent purchase requests
        recent_pr_query = f"""
            SELECT pr.*, t.name as tenant_name, s.name as storeroom_name
            FROM purchase_requests pr
            LEFT JOIN tenants t ON pr.requesting_tenant_id = t.id
            LEFT JOIN storerooms s ON pr.storeroom_id = s.id
            {tenant_condition}
            ORDER BY pr.created_at DESC
            LIMIT 5
        """

        recent_requests = db_manager.execute_query(recent_pr_query, tenant_params)

        # Get urgent requests (high priority and due soon)
        urgent_pr_query = f"""
            SELECT pr.*, t.name as tenant_name, s.name as storeroom_name
            FROM purchase_requests pr
            LEFT JOIN tenants t ON pr.requesting_tenant_id = t.id
            LEFT JOIN storerooms s ON pr.storeroom_id = s.id
            {tenant_condition}
            {'AND' if tenant_condition else 'WHERE'} (
                pr.priority = 'high' OR
                (pr.required_date <= date('now', '+7 days') AND pr.status IN ('draft', 'submitted'))
            )
            ORDER BY pr.required_date ASC, pr.priority DESC
            LIMIT 5
        """

        urgent_requests = db_manager.execute_query(urgent_pr_query, tenant_params)

        # Get delivery note statistics
        dn_stats_query = f"""
            SELECT
                COUNT(*) as total_deliveries,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_deliveries,
                SUM(CASE WHEN status = 'dispatched' THEN 1 ELSE 0 END) as in_transit_deliveries,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_deliveries,
                SUM(CASE WHEN status = 'received' THEN 1 ELSE 0 END) as received_deliveries,
                SUM(total_amount) as total_delivery_value
            FROM delivery_notes dn
            {tenant_condition.replace('pr.requesting_tenant_id', 'dn.to_tenant_id') if tenant_condition else ''}
        """

        dn_stats = db_manager.execute_query(dn_stats_query, tenant_params)
        dn_data = dn_stats[0] if dn_stats else {
            'total_deliveries': 0, 'draft_deliveries': 0, 'in_transit_deliveries': 0,
            'delivered_deliveries': 0, 'received_deliveries': 0, 'total_delivery_value': 0
        }

        # Get recent delivery notes
        recent_dn_query = f"""
            SELECT dn.*, t.name as tenant_name
            FROM delivery_notes dn
            LEFT JOIN tenants t ON dn.to_tenant_id = t.id
            {tenant_condition.replace('pr.requesting_tenant_id', 'dn.to_tenant_id') if tenant_condition else ''}
            ORDER BY dn.created_at DESC
            LIMIT 5
        """

        recent_deliveries = db_manager.execute_query(recent_dn_query, tenant_params)

        # Prepare dashboard response
        dashboard_data = {
            'purchase_requests': {
                'total': pr_data.get('total_requests', 0),
                'draft': pr_data.get('draft_requests', 0),
                'pending': pr_data.get('pending_requests', 0),
                'approved': pr_data.get('approved_requests', 0),
                'rejected': pr_data.get('rejected_requests', 0),
                'total_value': float(pr_data.get('total_estimated_value', 0) or 0)
            },
            'purchase_orders': {
                'total': po_data.get('total_orders', 0),
                'draft': po_data.get('draft_orders', 0),
                'approved': po_data.get('approved_orders', 0),
                'completed': po_data.get('completed_orders', 0),
                'total_value': float(po_data.get('total_order_value', 0) or 0)
            },
            'deliveries': {
                'total': dn_data.get('total_deliveries', 0),
                'draft': dn_data.get('draft_deliveries', 0),
                'in_transit': dn_data.get('in_transit_deliveries', 0),
                'delivered': dn_data.get('delivered_deliveries', 0),
                'received': dn_data.get('received_deliveries', 0),
                'total_value': float(dn_data.get('total_delivery_value', 0) or 0)
            },
            'recent_activities': {
                'requests': recent_requests or [],
                'deliveries': recent_deliveries or []
            },
            'recent_requests': recent_requests or [],
            'urgent_requests': urgent_requests or []
        }

        return jsonify(dashboard_data)

    except Exception as e:
        return jsonify({'message': f'Error fetching dashboard data: {str(e)}'}), 500

# Purchase Requests Routes
@procurement_bp.route('/api/procurement/purchase-requests', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_requests():
    """Get all purchase requests with filtering and pagination"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('limit', 20, type=int)
        status = request.args.get('status')
        priority = request.args.get('priority')
        
        # Base query
        query = """
            SELECT pr.*, 
                   rt.name as requesting_tenant_name,
                   ht.name as hub_tenant_name,
                   s.name as storeroom_name,
                   u.username as created_by_username
            FROM purchase_requests pr
            LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
            LEFT JOIN tenants ht ON pr.hub_tenant_id = ht.id
            LEFT JOIN storerooms s ON pr.storeroom_id = s.id
            LEFT JOIN users u ON pr.created_by = u.id
            WHERE 1=1
        """
        params = []
        
        # Apply tenant-based filtering
        if user_role == 'admin':
            # Admin can see all requests
            pass
        elif user_role == 'hub_admin':
            # Hub admin can see all franchise requests and their own
            query += " AND (pr.requesting_tenant_id != pr.hub_tenant_id OR pr.hub_tenant_id = ?)"
            params.append(user_tenant_id)
        else:
            # Franchise users can only see their own requests
            query += " AND pr.requesting_tenant_id = ?"
            params.append(user_tenant_id)
        
        # Apply filters
        if status:
            query += " AND pr.status = ?"
            params.append(status)
        
        if priority:
            query += " AND pr.priority = ?"
            params.append(priority)
        
        # Add ordering
        query += " ORDER BY pr.created_at DESC"
        
        # Execute query
        purchase_requests = db_manager.execute_query(query, tuple(params))
        
        # Get items for each request
        for request_item in purchase_requests:
            items_query = """
                SELECT * FROM purchase_request_items 
                WHERE purchase_request_id = ?
                ORDER BY id
            """
            items = db_manager.execute_query(items_query, (request_item['id'],))
            request_item['items'] = items
        
        # Apply pagination
        total = len(purchase_requests)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_requests = purchase_requests[start:end]
        
        return jsonify({
            'data': paginated_requests,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({'message': f'Error fetching purchase requests: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_request(request_id):
    """Get a specific purchase request"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Get the purchase request with related data
        query = """
            SELECT pr.*, 
                   rt.name as requesting_tenant_name,
                   ht.name as hub_tenant_name,
                   s.name as storeroom_name,
                   u.username as created_by_username
            FROM purchase_requests pr
            LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
            LEFT JOIN tenants ht ON pr.hub_tenant_id = ht.id
            LEFT JOIN storerooms s ON pr.storeroom_id = s.id
            LEFT JOIN users u ON pr.created_by = u.id
            WHERE pr.id = ?
        """
        
        result = db_manager.execute_query(query, (request_id,))
        if not result:
            return jsonify({'message': 'Purchase request not found'}), 404
        
        purchase_request = result[0]
        
        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request['requesting_tenant_id'] != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403
        
        # Get items for this request
        items_query = """
            SELECT * FROM purchase_request_items 
            WHERE purchase_request_id = ?
            ORDER BY id
        """
        items = db_manager.execute_query(items_query, (request_id,))
        purchase_request['items'] = items
        
        return jsonify(purchase_request)
        
    except Exception as e:
        return jsonify({'message': f'Error fetching purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def create_purchase_request():
    """Create a new purchase request"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')

        # Validate required fields
        required_fields = ['items', 'priority', 'required_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'Missing required field: {field}'}), 400

        # Validate items
        if not data['items'] or len(data['items']) == 0:
            return jsonify({'message': 'At least one item is required'}), 400

        for item in data['items']:
            if not all(key in item for key in ['item_name', 'requested_quantity', 'unit']):
                return jsonify({'message': 'Each item must have item_name, requested_quantity, and unit'}), 400

        # Generate request number
        request_number = generate_request_number()

        # Calculate total estimated amount
        total_estimated_amount = sum(
            float(item.get('estimated_unit_price', 0)) * int(item.get('requested_quantity', 0))
            for item in data['items']
        )

        # Create new purchase request
        request_data = {
            'request_number': request_number,
            'requesting_tenant_id': user_tenant_id,
            'hub_tenant_id': 1,  # Mayiladuthurai hub
            'priority': data['priority'],
            'status': 'draft',
            'request_date': datetime.now().strftime('%Y-%m-%d'),
            'required_date': data['required_date'],
            'notes': data.get('notes', ''),
            'total_estimated_amount': total_estimated_amount,
            'storeroom_id': data.get('storeroom_id'),
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        # Insert purchase request
        request_id = db_manager.insert_record('purchase_requests', request_data)

        # Insert items
        for item in data['items']:
            item_data = {
                'purchase_request_id': request_id,
                'item_name': item['item_name'],
                'description': item.get('description', ''),
                'requested_quantity': item['requested_quantity'],
                'unit': item['unit'],
                'estimated_unit_price': item.get('estimated_unit_price', 0),
                'total_estimated_amount': float(item.get('estimated_unit_price', 0)) * int(item['requested_quantity']),
                'notes': item.get('notes', ''),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            db_manager.insert_record('purchase_request_items', item_data)

        # Get the created request with items
        created_request = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )[0]
        
        items = db_manager.execute_query(
            "SELECT * FROM purchase_request_items WHERE purchase_request_id = ?", (request_id,)
        )
        created_request['items'] = items

        return jsonify(created_request), 201

    except Exception as e:
        return jsonify({'message': f'Error creating purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>', methods=['PUT'])
@token_required
@require_module_access('PROCUREMENT')
def update_purchase_request(request_id):
    """Update a purchase request"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Get existing request
        existing = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )
        if not existing:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = existing[0]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request['requesting_tenant_id'] != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        # Can only update draft requests
        if purchase_request['status'] != 'draft':
            return jsonify({'message': 'Only draft requests can be updated'}), 400

        # Update fields
        update_data = {
            'updated_at': datetime.now().isoformat()
        }

        if 'priority' in data:
            update_data['priority'] = data['priority']
        if 'required_date' in data:
            update_data['required_date'] = data['required_date']
        if 'notes' in data:
            update_data['notes'] = data['notes']
        if 'storeroom_id' in data:
            update_data['storeroom_id'] = data['storeroom_id']

        # Update items if provided
        if 'items' in data:
            # Delete existing items
            db_manager.execute_update(
                "DELETE FROM purchase_request_items WHERE purchase_request_id = ?",
                (request_id,)
            )

            # Insert new items
            total_estimated_amount = 0
            for item in data['items']:
                item_total = float(item.get('estimated_unit_price', 0)) * int(item['requested_quantity'])
                total_estimated_amount += item_total

                item_data = {
                    'purchase_request_id': request_id,
                    'item_name': item['item_name'],
                    'description': item.get('description', ''),
                    'requested_quantity': item['requested_quantity'],
                    'unit': item['unit'],
                    'estimated_unit_price': item.get('estimated_unit_price', 0),
                    'total_estimated_amount': item_total,
                    'notes': item.get('notes', ''),
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
                db_manager.insert_record('purchase_request_items', item_data)

            update_data['total_estimated_amount'] = total_estimated_amount

        # Update the purchase request
        set_clause = ', '.join([f"{key} = ?" for key in update_data.keys()])
        values = list(update_data.values()) + [request_id]

        db_manager.execute_update(
            f"UPDATE purchase_requests SET {set_clause} WHERE id = ?",
            tuple(values)
        )

        # Get updated request with items
        updated_request = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )[0]

        items = db_manager.execute_query(
            "SELECT * FROM purchase_request_items WHERE purchase_request_id = ?", (request_id,)
        )
        updated_request['items'] = items

        return jsonify(updated_request)

    except Exception as e:
        return jsonify({'message': f'Error updating purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>/submit', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def submit_purchase_request(request_id):
    """Submit a purchase request for approval"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Get existing request
        existing = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )
        if not existing:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = existing[0]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if purchase_request['requesting_tenant_id'] != user_tenant_id:
                return jsonify({'message': 'Access denied'}), 403

        # Can only submit draft requests
        if purchase_request['status'] != 'draft':
            return jsonify({'message': 'Only draft requests can be submitted'}), 400

        # Update status
        db_manager.execute_update(
            "UPDATE purchase_requests SET status = ?, updated_at = ? WHERE id = ?",
            ('submitted', datetime.now().isoformat(), request_id)
        )

        # Get updated request
        updated_request = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )[0]

        return jsonify({'message': 'Purchase request submitted successfully', 'request': updated_request})

    except Exception as e:
        return jsonify({'message': f'Error submitting purchase request: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:request_id>/approve', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def approve_purchase_request(request_id):
    """Approve a purchase request"""
    try:
        user = request.current_user

        # Get existing request
        existing = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )
        if not existing:
            return jsonify({'message': 'Purchase request not found'}), 404

        purchase_request = existing[0]

        # Can only approve submitted requests
        if purchase_request['status'] != 'submitted':
            return jsonify({'message': 'Only submitted requests can be approved'}), 400

        # Update status
        db_manager.execute_update(
            "UPDATE purchase_requests SET status = ?, updated_at = ? WHERE id = ?",
            ('approved', datetime.now().isoformat(), request_id)
        )

        # Get updated request
        updated_request = db_manager.execute_query(
            "SELECT * FROM purchase_requests WHERE id = ?", (request_id,)
        )[0]

        return jsonify({'message': 'Purchase request approved successfully', 'request': updated_request})

    except Exception as e:
        return jsonify({'message': f'Error approving purchase request: {str(e)}'}), 500

# Purchase Orders Routes
@procurement_bp.route('/api/procurement/purchase-orders', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_orders():
    """Get all purchase orders with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        search = request.args.get('search')

        # Build base query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all purchase orders
            base_query = """
                SELECT po.*, t.name as tenant_name, t.site_code,
                       pr.request_number
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                WHERE 1=1
            """
            params = []
        else:
            # Franchise users can only see their own purchase orders
            base_query = """
                SELECT po.*, t.name as tenant_name, t.site_code,
                       pr.request_number
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                WHERE po.tenant_id = ?
            """
            params = [user_tenant_id]

        # Add filters
        if status:
            base_query += " AND po.status = ?"
            params.append(status)

        if search:
            base_query += " AND (po.po_number LIKE ? OR po.notes LIKE ?)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])

        # Add ordering
        base_query += " ORDER BY po.created_at DESC"

        # Execute query
        all_orders = db_manager.execute_query(base_query, params)

        # Calculate pagination
        total_count = len(all_orders)
        total_pages = (total_count + limit - 1) // limit
        offset = (page - 1) * limit
        orders = all_orders[offset:offset + limit]

        return jsonify({
            'success': True,
            'data': orders,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'per_page': limit
            }
        })

    except Exception as e:
        return jsonify({'message': f'Error fetching purchase orders: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:order_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_purchase_order(order_id):
    """Get a specific purchase order"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Build query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            query = """
                SELECT po.*, t.name as tenant_name, t.site_code,
                       pr.request_number
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                WHERE po.id = ?
            """
            params = [order_id]
        else:
            query = """
                SELECT po.*, t.name as tenant_name, t.site_code,
                       pr.request_number
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                WHERE po.id = ? AND po.tenant_id = ?
            """
            params = [order_id, user_tenant_id]

        result = db_manager.execute_query(query, params)

        if not result:
            return jsonify({'message': 'Purchase order not found'}), 404

        purchase_order = result[0]

        # Get items for this purchase order
        items_query = "SELECT * FROM purchase_order_items WHERE purchase_order_id = ?"
        items = db_manager.execute_query(items_query, [order_id])
        purchase_order['items'] = items

        return jsonify(purchase_order)

    except Exception as e:
        return jsonify({'message': f'Error fetching purchase order: {str(e)}'}), 500

# Proforma Invoices Routes
@procurement_bp.route('/api/procurement/proforma-invoices', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_proforma_invoices():
    """Get all proforma invoices with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        search = request.args.get('search')

        # Build base query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all proforma invoices
            base_query = """
                SELECT pi.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM proforma_invoices pi
                LEFT JOIN tenants ft ON pi.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pi.to_tenant_id = tt.id
                WHERE 1=1
            """
            params = []
        else:
            # Franchise users can only see proforma invoices they're involved in
            base_query = """
                SELECT pi.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM proforma_invoices pi
                LEFT JOIN tenants ft ON pi.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pi.to_tenant_id = tt.id
                WHERE (pi.from_tenant_id = ? OR pi.to_tenant_id = ?)
            """
            params = [user_tenant_id, user_tenant_id]

        # Add filters
        if status:
            base_query += " AND pi.status = ?"
            params.append(status)

        if search:
            base_query += " AND (pi.proforma_number LIKE ? OR pi.notes LIKE ?)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])

        # Add ordering
        base_query += " ORDER BY pi.created_at DESC"

        # Execute query
        all_invoices = db_manager.execute_query(base_query, params)

        # Calculate pagination
        total_count = len(all_invoices)
        total_pages = (total_count + limit - 1) // limit
        offset = (page - 1) * limit
        invoices = all_invoices[offset:offset + limit]

        return jsonify({
            'success': True,
            'data': invoices,
            'total_pages': total_pages,
            'total_count': total_count,
            'current_page': page,
            'per_page': limit
        })

    except Exception as e:
        return jsonify({'message': f'Error fetching proforma invoices: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/proforma-invoices/<int:invoice_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_proforma_invoice(invoice_id):
    """Get a specific proforma invoice"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Build query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            query = """
                SELECT pi.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM proforma_invoices pi
                LEFT JOIN tenants ft ON pi.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pi.to_tenant_id = tt.id
                WHERE pi.id = ?
            """
            params = [invoice_id]
        else:
            query = """
                SELECT pi.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM proforma_invoices pi
                LEFT JOIN tenants ft ON pi.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pi.to_tenant_id = tt.id
                WHERE pi.id = ? AND (pi.from_tenant_id = ? OR pi.to_tenant_id = ?)
            """
            params = [invoice_id, user_tenant_id, user_tenant_id]

        result = db_manager.execute_query(query, params)

        if not result:
            return jsonify({'message': 'Proforma invoice not found'}), 404

        proforma_invoice = result[0]

        # Get items for this proforma invoice
        items_query = "SELECT * FROM proforma_invoice_items WHERE proforma_invoice_id = ?"
        items = db_manager.execute_query(items_query, [invoice_id])
        proforma_invoice['items'] = items

        return jsonify(proforma_invoice)

    except Exception as e:
        return jsonify({'message': f'Error fetching proforma invoice: {str(e)}'}), 500

# Delivery Notes Routes
@procurement_bp.route('/api/procurement/delivery-notes', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_delivery_notes():
    """Get all delivery notes with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        search = request.args.get('search')

        # Build base query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all delivery notes
            base_query = """
                SELECT dn.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM delivery_notes dn
                LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
                WHERE 1=1
            """
            params = []
        else:
            # Franchise users can only see delivery notes they're involved in
            base_query = """
                SELECT dn.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM delivery_notes dn
                LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
                WHERE (dn.from_tenant_id = ? OR dn.to_tenant_id = ?)
            """
            params = [user_tenant_id, user_tenant_id]

        # Add filters
        if status:
            base_query += " AND dn.status = ?"
            params.append(status)

        if search:
            base_query += " AND (dn.delivery_number LIKE ? OR dn.notes LIKE ?)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])

        # Add ordering
        base_query += " ORDER BY dn.created_at DESC"

        # Execute query
        all_notes = db_manager.execute_query(base_query, params)

        # Calculate pagination
        total_count = len(all_notes)
        total_pages = (total_count + limit - 1) // limit
        offset = (page - 1) * limit
        notes = all_notes[offset:offset + limit]

        return jsonify({
            'success': True,
            'data': notes,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'per_page': limit
            }
        })

    except Exception as e:
        return jsonify({'message': f'Error fetching delivery notes: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:note_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_delivery_note(note_id):
    """Get a specific delivery note"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Build query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            query = """
                SELECT dn.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM delivery_notes dn
                LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
                WHERE dn.id = ?
            """
            params = [note_id]
        else:
            query = """
                SELECT dn.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM delivery_notes dn
                LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
                WHERE dn.id = ? AND (dn.from_tenant_id = ? OR dn.to_tenant_id = ?)
            """
            params = [note_id, user_tenant_id, user_tenant_id]

        result = db_manager.execute_query(query, params)

        if not result:
            return jsonify({'message': 'Delivery note not found'}), 404

        delivery_note = result[0]

        # Get items for this delivery note
        items_query = "SELECT * FROM delivery_note_items WHERE delivery_note_id = ?"
        items = db_manager.execute_query(items_query, [note_id])
        delivery_note['items'] = items

        return jsonify(delivery_note)

    except Exception as e:
        return jsonify({'message': f'Error fetching delivery note: {str(e)}'}), 500

# Payment Transactions Routes
@procurement_bp.route('/api/procurement/payment-transactions', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_payment_transactions():
    """Get all payment transactions with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        search = request.args.get('search')

        # Build base query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all payment transactions
            base_query = """
                SELECT pt.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM payment_transactions pt
                LEFT JOIN tenants ft ON pt.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pt.to_tenant_id = tt.id
                WHERE 1=1
            """
            params = []
        else:
            # Franchise users can only see payment transactions they're involved in
            base_query = """
                SELECT pt.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code
                FROM payment_transactions pt
                LEFT JOIN tenants ft ON pt.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON pt.to_tenant_id = tt.id
                WHERE (pt.from_tenant_id = ? OR pt.to_tenant_id = ?)
            """
            params = [user_tenant_id, user_tenant_id]

        # Add filters
        if status:
            base_query += " AND pt.status = ?"
            params.append(status)

        if search:
            base_query += " AND (pt.transaction_number LIKE ? OR pt.notes LIKE ?)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])

        # Add ordering
        base_query += " ORDER BY pt.created_at DESC"

        # Execute query
        all_transactions = db_manager.execute_query(base_query, params)

        # Calculate pagination
        total_count = len(all_transactions)
        total_pages = (total_count + limit - 1) // limit
        offset = (page - 1) * limit
        transactions = all_transactions[offset:offset + limit]

        return jsonify({
            'success': True,
            'data': transactions,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'per_page': limit
            }
        })

    except Exception as e:
        return jsonify({'message': f'Error fetching payment transactions: {str(e)}'}), 500

# Inventory Transfers Routes
@procurement_bp.route('/api/procurement/inventory-transfers', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_inventory_transfers():
    """Get all inventory transfers with filtering and pagination"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        user_role = user.get('role')

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        search = request.args.get('search')

        # Build base query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            # Hub users can see all inventory transfers
            base_query = """
                SELECT it.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code,
                       fs.name as from_storeroom_name,
                       ts.name as to_storeroom_name
                FROM inventory_transfers it
                LEFT JOIN tenants ft ON it.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON it.to_tenant_id = tt.id
                LEFT JOIN storerooms fs ON it.from_storeroom_id = fs.id
                LEFT JOIN storerooms ts ON it.to_storeroom_id = ts.id
                WHERE 1=1
            """
            params = []
        else:
            # Franchise users can only see inventory transfers they're involved in
            base_query = """
                SELECT it.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code,
                       fs.name as from_storeroom_name,
                       ts.name as to_storeroom_name
                FROM inventory_transfers it
                LEFT JOIN tenants ft ON it.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON it.to_tenant_id = tt.id
                LEFT JOIN storerooms fs ON it.from_storeroom_id = fs.id
                LEFT JOIN storerooms ts ON it.to_storeroom_id = ts.id
                WHERE (it.from_tenant_id = ? OR it.to_tenant_id = ?)
            """
            params = [user_tenant_id, user_tenant_id]

        # Add filters
        if status:
            base_query += " AND it.status = ?"
            params.append(status)

        if search:
            base_query += " AND (it.transfer_number LIKE ? OR it.notes LIKE ?)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])

        # Add ordering
        base_query += " ORDER BY it.created_at DESC"

        # Execute query
        all_transfers = db_manager.execute_query(base_query, params)

        # Calculate pagination
        total_count = len(all_transfers)
        total_pages = (total_count + limit - 1) // limit
        offset = (page - 1) * limit
        transfers = all_transfers[offset:offset + limit]

        return jsonify({
            'success': True,
            'data': transfers,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'per_page': limit
            }
        })

    except Exception as e:
        return jsonify({'message': f'Error fetching inventory transfers: {str(e)}'}), 500

@procurement_bp.route('/api/procurement/inventory-transfers/<int:transfer_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_inventory_transfer(transfer_id):
    """Get a specific inventory transfer"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Build query with tenant filtering
        if user_role in ['admin', 'hub_admin']:
            query = """
                SELECT it.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code,
                       fs.name as from_storeroom_name,
                       ts.name as to_storeroom_name
                FROM inventory_transfers it
                LEFT JOIN tenants ft ON it.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON it.to_tenant_id = tt.id
                LEFT JOIN storerooms fs ON it.from_storeroom_id = fs.id
                LEFT JOIN storerooms ts ON it.to_storeroom_id = ts.id
                WHERE it.id = ?
            """
            params = [transfer_id]
        else:
            query = """
                SELECT it.*,
                       ft.name as from_tenant_name, ft.site_code as from_site_code,
                       tt.name as to_tenant_name, tt.site_code as to_site_code,
                       fs.name as from_storeroom_name,
                       ts.name as to_storeroom_name
                FROM inventory_transfers it
                LEFT JOIN tenants ft ON it.from_tenant_id = ft.id
                LEFT JOIN tenants tt ON it.to_tenant_id = tt.id
                LEFT JOIN storerooms fs ON it.from_storeroom_id = fs.id
                LEFT JOIN storerooms ts ON it.to_storeroom_id = ts.id
                WHERE it.id = ? AND (it.from_tenant_id = ? OR it.to_tenant_id = ?)
            """
            params = [transfer_id, user_tenant_id, user_tenant_id]

        result = db_manager.execute_query(query, params)

        if not result:
            return jsonify({'message': 'Inventory transfer not found'}), 404

        inventory_transfer = result[0]

        # Get items for this inventory transfer
        items_query = "SELECT * FROM inventory_transfer_items WHERE inventory_transfer_id = ?"
        items = db_manager.execute_query(items_query, [transfer_id])
        inventory_transfer['items'] = items

        return jsonify(inventory_transfer)

    except Exception as e:
        return jsonify({'message': f'Error fetching inventory transfer: {str(e)}'}), 500

# Storeroom Management Routes
@procurement_bp.route('/api/storerooms', methods=['GET'])
@token_required
def get_storerooms():
    """Get all storerooms with tenant-based filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Base query
        query = """
            SELECT s.*, t.name as tenant_name, t.site_code
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.status = 'active'
        """
        params = []

        # Apply tenant-based filtering
        if user_role == 'admin':
            # Admin can see all storerooms
            pass
        elif user_role == 'hub_admin':
            # Hub admin can see all storerooms
            pass
        else:
            # Franchise users can only see their own storerooms
            query += " AND s.tenant_id = ?"
            params.append(user_tenant_id)

        query += " ORDER BY t.name, s.name"

        storerooms = db_manager.execute_query(query, tuple(params))

        return jsonify({
            'success': True,
            'data': storerooms
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/storerooms/<int:storeroom_id>', methods=['GET'])
@token_required
def get_storeroom(storeroom_id):
    """Get a specific storeroom"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        query = """
            SELECT s.*, t.name as tenant_name, t.site_code
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.id = ?
        """

        result = db_manager.execute_query(query, (storeroom_id,))
        if not result:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404

        storeroom = result[0]

        # Check access permissions
        if user_role not in ['admin', 'hub_admin']:
            if storeroom['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403

        return jsonify({
            'success': True,
            'data': storeroom
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/storerooms', methods=['POST'])
@token_required
@require_role(['admin', 'hub_admin'])
def create_storeroom():
    """Create a new storeroom"""
    try:
        data = request.get_json()
        user = request.current_user

        # Validate required fields
        required_fields = ['storeroom_id', 'name', 'tenant_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400

        # Check if storeroom_id already exists
        existing = db_manager.execute_query(
            "SELECT id FROM storerooms WHERE storeroom_id = ?",
            (data['storeroom_id'],)
        )
        if existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom ID already exists'
            }), 400

        # Create new storeroom
        storeroom_data = {
            'storeroom_id': data['storeroom_id'],
            'name': data['name'],
            'description': data.get('description', ''),
            'tenant_id': data['tenant_id'],
            'location_details': data.get('location_details', ''),
            'capacity': data.get('capacity'),
            'status': data.get('status', 'active'),
            'manager_name': data.get('manager_name', ''),
            'manager_contact': data.get('manager_contact', ''),
            'created_by': user.get('id'),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

        storeroom_id = db_manager.insert_record('storerooms', storeroom_data)

        # Get the created storeroom with tenant info
        created_storeroom = db_manager.execute_query("""
            SELECT s.*, t.name as tenant_name, t.site_code
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.id = ?
        """, (storeroom_id,))[0]

        return jsonify({
            'success': True,
            'data': created_storeroom,
            'message': 'Storeroom created successfully'
        }), 201

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/storerooms/<int:storeroom_id>', methods=['PUT'])
@token_required
@require_role(['admin', 'hub_admin'])
def update_storeroom(storeroom_id):
    """Update a storeroom"""
    try:
        data = request.get_json()
        user = request.current_user

        # Check if storeroom exists
        existing = db_manager.execute_query(
            "SELECT * FROM storerooms WHERE id = ?", (storeroom_id,)
        )
        if not existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404

        # Check if storeroom_id already exists (excluding current storeroom)
        if 'storeroom_id' in data:
            existing_id = db_manager.execute_query(
                "SELECT id FROM storerooms WHERE storeroom_id = ? AND id != ?",
                (data['storeroom_id'], storeroom_id)
            )
            if existing_id:
                return jsonify({
                    'success': False,
                    'error': 'Storeroom ID already exists'
                }), 400

        # Update fields
        update_data = {
            'updated_at': datetime.now().isoformat()
        }

        allowed_fields = [
            'storeroom_id', 'name', 'description', 'tenant_id',
            'location_details', 'capacity', 'status', 'manager_name', 'manager_contact'
        ]

        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]

        # Update the storeroom
        set_clause = ', '.join([f"{key} = ?" for key in update_data.keys()])
        values = list(update_data.values()) + [storeroom_id]

        db_manager.execute_update(
            f"UPDATE storerooms SET {set_clause} WHERE id = ?",
            tuple(values)
        )

        # Get updated storeroom with tenant info
        updated_storeroom = db_manager.execute_query("""
            SELECT s.*, t.name as tenant_name, t.site_code
            FROM storerooms s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            WHERE s.id = ?
        """, (storeroom_id,))[0]

        return jsonify({
            'success': True,
            'data': updated_storeroom,
            'message': 'Storeroom updated successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/storerooms/<int:storeroom_id>', methods=['DELETE'])
@token_required
@require_role(['admin', 'hub_admin'])
def delete_storeroom(storeroom_id):
    """Delete a storeroom (soft delete by setting status to inactive)"""
    try:
        # Check if storeroom exists
        existing = db_manager.execute_query(
            "SELECT * FROM storerooms WHERE id = ?", (storeroom_id,)
        )
        if not existing:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404

        # Soft delete by setting status to inactive
        db_manager.execute_update(
            "UPDATE storerooms SET status = ?, updated_at = ? WHERE id = ?",
            ('inactive', datetime.now().isoformat(), storeroom_id)
        )

        return jsonify({
            'success': True,
            'message': 'Storeroom deleted successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Purchase Order Management Routes
@procurement_bp.route('/api/procurement/purchase-orders-enhanced', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def get_purchase_orders_enhanced():
    """Get purchase orders with filtering"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')

        # Get query parameters
        status = request.args.get('status')

        # Base query
        query = """
            SELECT po.*,
                   t.name as tenant_name, t.site_code,
                   pr.request_number as pr_number,
                   u.username as created_by_username,
                   COUNT(poi.id) as items_count
            FROM purchase_orders po
            LEFT JOIN tenants t ON po.tenant_id = t.id
            LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
            LEFT JOIN users u ON po.created_by = u.id
            LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
            WHERE 1=1
        """
        params = []

        # Apply status filter
        if status:
            query += " AND po.status = ?"
            params.append(status)

        query += " GROUP BY po.id ORDER BY po.created_at DESC"

        purchase_orders = db_manager.execute_query(query, tuple(params))

        return jsonify({
            'success': True,
            'data': purchase_orders
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/purchase-orders-enhanced/<int:po_id>', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def get_purchase_order_enhanced(po_id):
    """Get a specific purchase order with items"""
    try:
        from services.purchase_order_service import PurchaseOrderService
        po_service = PurchaseOrderService()

        purchase_order = po_service.get_purchase_order_with_items(po_id)

        if not purchase_order:
            return jsonify({
                'success': False,
                'error': 'Purchase order not found'
            }), 404

        return jsonify({
            'success': True,
            'data': purchase_order
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/purchase-requests/<int:pr_id>/generate-po', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def generate_purchase_order_from_pr(pr_id):
    """Generate a purchase order from an approved purchase request"""
    try:
        data = request.get_json()
        user = request.current_user

        from services.purchase_order_service import PurchaseOrderService
        po_service = PurchaseOrderService()

        result = po_service.generate_po_from_purchase_request(
            pr_id,
            supplier_id=data.get('supplier_id'),
            user_id=user.get('id')
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:po_id>/send', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def send_purchase_order(po_id):
    """Send purchase order to supplier"""
    try:
        user = request.current_user

        from services.purchase_order_service import PurchaseOrderService
        po_service = PurchaseOrderService()

        result = po_service.send_purchase_order(po_id, user.get('id'))

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:po_id>/confirm', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def confirm_purchase_order(po_id):
    """Confirm purchase order from supplier"""
    try:
        data = request.get_json()
        user = request.current_user

        from services.purchase_order_service import PurchaseOrderService
        po_service = PurchaseOrderService()

        result = po_service.confirm_purchase_order(
            po_id,
            user.get('id'),
            confirmation_details=data
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Delivery Note Management Routes
@procurement_bp.route('/api/procurement/delivery-notes-enhanced', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_delivery_notes_enhanced():
    """Get delivery notes with filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Get query parameters
        status = request.args.get('status')

        # Base query
        query = """
            SELECT dn.*,
                   ft.name as from_tenant_name, ft.site_code as from_site_code,
                   tt.name as to_tenant_name, tt.site_code as to_site_code,
                   po.po_number,
                   pr.request_number as pr_number,
                   u.username as created_by_username,
                   COUNT(dni.id) as items_count
            FROM delivery_notes dn
            LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
            LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
            LEFT JOIN purchase_orders po ON dn.purchase_order_id = po.id
            LEFT JOIN purchase_requests pr ON dn.purchase_request_id = pr.id
            LEFT JOIN users u ON dn.created_by = u.id
            LEFT JOIN delivery_note_items dni ON dn.id = dni.delivery_note_id
            WHERE 1=1
        """
        params = []

        # Apply tenant-based filtering
        if user_role not in ['admin', 'hub_admin']:
            query += " AND (dn.from_tenant_id = ? OR dn.to_tenant_id = ?)"
            params.extend([user_tenant_id, user_tenant_id])

        # Apply status filter
        if status:
            query += " AND dn.status = ?"
            params.append(status)

        query += " GROUP BY dn.id ORDER BY dn.created_at DESC"

        delivery_notes = db_manager.execute_query(query, tuple(params))

        return jsonify({
            'success': True,
            'data': delivery_notes
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/delivery-notes/create', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def get_delivery_note_create_form():
    """Get form data for creating a new delivery note"""
    try:
        user = request.current_user
        user_tenant_id = user.get('tenant_id')

        # Get available tenants for delivery
        tenants_query = "SELECT id, name, site_code FROM tenants WHERE id != ? ORDER BY name"
        tenants = db_manager.execute_query(tenants_query, (user_tenant_id,))

        # Get available purchase orders that can be converted to delivery notes
        po_query = """
            SELECT po.id, po.po_number, po.total_amount, t.name as requesting_tenant_name
            FROM purchase_orders po
            LEFT JOIN tenants t ON po.requesting_tenant_id = t.id
            WHERE po.status = 'confirmed' AND po.id NOT IN (
                SELECT DISTINCT purchase_order_id FROM delivery_notes
                WHERE purchase_order_id IS NOT NULL
            )
            ORDER BY po.created_at DESC
        """
        available_pos = db_manager.execute_query(po_query)

        # Get inventory items for manual delivery note creation
        inventory_query = """
            SELECT DISTINCT name, sku FROM inventory
            WHERE tenant_id = ?
            ORDER BY name
        """
        inventory_items = db_manager.execute_query(inventory_query, (user_tenant_id,))

        return jsonify({
            'success': True,
            'data': {
                'tenants': tenants,
                'available_purchase_orders': available_pos,
                'inventory_items': inventory_items,
                'delivery_types': [
                    {'value': 'hub_to_franchise', 'label': 'Hub to Franchise'},
                    {'value': 'supplier_to_hub', 'label': 'Supplier to Hub'},
                    {'value': 'transfer', 'label': 'Inter-branch Transfer'}
                ]
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/delivery-notes', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def create_delivery_note():
    """Create a new delivery note directly"""
    try:
        data = request.get_json()
        user = request.current_user

        # Validate required fields
        required_fields = ['to_tenant_id', 'delivery_type', 'items']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400

        from services.delivery_service import DeliveryService
        delivery_service = DeliveryService()

        # Calculate total amount
        total_amount = sum(
            float(item.get('unit_price', 0)) * int(item.get('quantity', 0))
            for item in data['items']
        )

        # Generate delivery number
        delivery_number = delivery_service._generate_delivery_number(data['to_tenant_id'])

        # Create delivery note in database
        insert_query = """
            INSERT INTO delivery_notes (
                delivery_number, from_tenant_id, to_tenant_id, delivery_type,
                status, delivery_date, expected_delivery_date, delivery_address,
                total_amount, notes, purchase_request_id, purchase_order_id,
                created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        params = (
            delivery_number,
            1,  # Hub tenant ID
            data['to_tenant_id'],
            data['delivery_type'],
            'prepared',
            data.get('delivery_date', datetime.now().date().isoformat()),
            data.get('expected_delivery_date'),
            data.get('delivery_address', ''),
            total_amount,
            data.get('notes', ''),
            data.get('purchase_request_id'),
            data.get('purchase_order_id'),
            user.get('id')
        )

        dn_id = db_manager.execute_query(insert_query, params)

        # Insert delivery note items
        for item in data['items']:
            item_insert_query = """
                INSERT INTO delivery_note_items (
                    delivery_note_id, item_name, description, quantity, unit,
                    unit_price, total_price, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            item_params = (
                dn_id,
                item['item_name'],
                item.get('description', ''),
                item['quantity'],
                item['unit'],
                item.get('unit_price', 0),
                float(item.get('unit_price', 0)) * int(item['quantity']),
                item.get('notes', '')
            )

            db_manager.execute_query(item_insert_query, item_params)

        # Get the created delivery note
        get_query = """
            SELECT dn.*,
                   ft.name as from_tenant_name, ft.site_code as from_site_code,
                   tt.name as to_tenant_name, tt.site_code as to_site_code
            FROM delivery_notes dn
            LEFT JOIN tenants ft ON dn.from_tenant_id = ft.id
            LEFT JOIN tenants tt ON dn.to_tenant_id = tt.id
            WHERE dn.id = ?
        """

        delivery_note = db_manager.execute_query(get_query, (dn_id,))[0]

        return jsonify({
            'success': True,
            'message': 'Delivery note created successfully',
            'delivery_note': dict(delivery_note)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/purchase-orders/<int:po_id>/create-delivery', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def create_delivery_note_from_po(po_id):
    """Create delivery note from confirmed purchase order"""
    try:
        data = request.get_json()
        user = request.current_user

        from services.delivery_service import DeliveryService
        delivery_service = DeliveryService()

        result = delivery_service.create_delivery_note_from_po(
            po_id,
            delivery_data=data,
            user_id=user.get('id')
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:dn_id>/dispatch', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def dispatch_delivery_note(dn_id):
    """Dispatch delivery note"""
    try:
        data = request.get_json()
        user = request.current_user

        from services.delivery_service import DeliveryService
        delivery_service = DeliveryService()

        result = delivery_service.dispatch_delivery_note(
            dn_id,
            dispatch_data=data,
            user_id=user.get('id')
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:dn_id>/receive', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def receive_delivery_note(dn_id):
    """Receive delivery and update inventory"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Check if user has access to this delivery note
        dn_query = """
            SELECT * FROM delivery_notes
            WHERE id = ? AND status = 'dispatched'
        """
        dn_results = db_manager.execute_query(dn_query, (dn_id,))

        if not dn_results:
            return jsonify({
                'success': False,
                'error': 'Delivery note not found or not dispatched'
            }), 404

        delivery_note = dn_results[0]

        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if delivery_note['to_tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403

        from services.delivery_service import DeliveryService
        delivery_service = DeliveryService()

        result = delivery_service.receive_delivery_note(
            dn_id,
            receipt_data=data,
            user_id=user.get('id')
        )

        # Create accounting entries for received goods
        try:
            ap_service = AccountsPayableService()

            # Get purchase order details
            po_query = """
                SELECT po.*, dn.total_amount
                FROM purchase_orders po
                JOIN delivery_notes dn ON po.id = dn.purchase_order_id
                WHERE dn.id = ?
            """
            po_results = db_manager.execute_query(po_query, (dn_id,))

            if po_results:
                po = po_results[0]

                # Create vendor if not exists
                vendor_code = f"VEN{po['id']:04d}"
                existing_vendor = ap_service.get_vendor_by_code(vendor_code, delivery_note['to_tenant_id'])

                if not existing_vendor:
                    vendor_data = {
                        'vendor_code': vendor_code,
                        'vendor_name': po.get('supplier_name', f"Vendor {po['id']}"),
                        'vendor_type': 'SUPPLIER',
                        'payment_terms': 'NET_30',
                        'tenant_id': delivery_note['to_tenant_id'],
                        'is_active': True
                    }
                    vendor_id = ap_service.create_vendor(vendor_data)
                else:
                    vendor_id = existing_vendor['id']

                # Create purchase invoice
                invoice_data = {
                    'vendor_id': vendor_id,
                    'invoice_number': f"PI-{po['po_number']}",
                    'invoice_date': datetime.now().date().isoformat(),
                    'due_date': (datetime.now() + timedelta(days=30)).date().isoformat(),
                    'total_amount': po['total_amount'],
                    'status': 'RECEIVED',
                    'tenant_id': delivery_note['to_tenant_id'],
                    'reference_type': 'DELIVERY_NOTE',
                    'reference_id': dn_id,
                    'po_number': po['po_number'],
                    'created_by': user.get('id')
                }

                # Get line items from delivery note
                items_query = "SELECT * FROM delivery_note_items WHERE delivery_note_id = ?"
                items = db_manager.execute_query(items_query, (dn_id,))

                invoice_line_items = []
                for i, item in enumerate(items, 1):
                    invoice_line_items.append({
                        'line_number': i,
                        'item_description': item.get('item_name', f"Item {item['id']}"),
                        'quantity': item.get('quantity_received', item.get('quantity', 1)),
                        'unit_price': item.get('unit_price', 0),
                        'line_total': item.get('total_price', 0)
                    })

                if invoice_line_items:
                    invoice_id = ap_service.create_purchase_invoice(invoice_data, invoice_line_items)
                    ap_service.approve_purchase_invoice(invoice_id, user.get('id'))

                    # Add reference to the result
                    result['accounting'] = {
                        'invoice_id': invoice_id,
                        'vendor_id': vendor_id,
                        'message': 'Accounting entries created successfully'
                    }

        except Exception as accounting_error:
            # Log the error but don't fail the delivery receipt
            import logging
            logging.error(f"Error creating accounting entries for delivery {dn_id}: {str(accounting_error)}")
            result['accounting_warning'] = f"Delivery received but accounting entries failed: {str(accounting_error)}"

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Automated Procurement Routes
@procurement_bp.route('/api/procurement/automated/check-inventory', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
@require_role(['admin', 'hub_admin'])
def trigger_automated_procurement_check():
    """Manually trigger automated procurement check"""
    try:
        automated_procurement_service.run_automated_check()

        return jsonify({
            'success': True,
            'message': 'Automated procurement check completed successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/automated/status', methods=['GET'])
@token_required
@require_module_access('PROCUREMENT')
def get_automated_procurement_status():
    """Get status of automated procurement system"""
    try:
        # Get storerooms with auto reorder enabled
        auto_storerooms_query = """
            SELECT COUNT(*) as count
            FROM storerooms
            WHERE auto_reorder_enabled = 1
            AND status = 'active'
            AND deleted_at IS NULL
        """
        auto_storerooms = db_manager.execute_query(auto_storerooms_query)

        # Get recent automated purchase requests (last 7 days)
        recent_auto_prs_query = """
            SELECT COUNT(*) as count
            FROM purchase_requests
            WHERE request_type = 'automated'
            AND created_at >= datetime('now', '-7 days')
        """
        recent_auto_prs = db_manager.execute_query(recent_auto_prs_query)

        # Get low stock items count
        low_stock_query = """
            SELECT COUNT(*) as count
            FROM inventory i
            JOIN storerooms s ON i.storeroom_id = s.id
            WHERE i.current_quantity <= s.min_quantity_threshold
            AND i.status = 'active'
            AND s.status = 'active'
            AND s.deleted_at IS NULL
        """
        low_stock_items = db_manager.execute_query(low_stock_query)

        return jsonify({
            'success': True,
            'data': {
                'auto_reorder_storerooms': auto_storerooms[0]['count'] if auto_storerooms else 0,
                'recent_automated_requests': recent_auto_prs[0]['count'] if recent_auto_prs else 0,
                'low_stock_items': low_stock_items[0]['count'] if low_stock_items else 0,
                'last_check': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/api/procurement/delivery-notes/<int:dn_id>/confirm', methods=['POST'])
@token_required
@require_module_access('PROCUREMENT')
def confirm_delivery_note(dn_id):
    """Confirm delivery receipt - alias for receive endpoint to match frontend API calls"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')

        # Check if user has access to this delivery note
        dn_query = """
            SELECT * FROM delivery_notes
            WHERE id = ? AND status IN ('dispatched', 'in_transit')
        """
        dn_results = db_manager.execute_query(dn_query, (dn_id,))

        if not dn_results:
            return jsonify({
                'success': False,
                'error': 'Delivery note not found or not available for confirmation'
            }), 404

        delivery_note = dn_results[0]

        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if delivery_note['to_tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403

        # Use the delivery service to receive the delivery note
        from services.delivery_service import DeliveryService
        delivery_service = DeliveryService()

        result = delivery_service.receive_delivery_note(
            dn_id,
            receipt_data=data,
            user_id=user.get('id')
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
