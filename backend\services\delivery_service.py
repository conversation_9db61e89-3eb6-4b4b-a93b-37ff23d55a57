"""
Delivery Service
Handles delivery challan/note creation and inventory updates
"""

from database_manager import db_manager
from datetime import datetime, timedelta
import logging

class DeliveryService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_delivery_note_from_po(self, po_id, delivery_data, user_id):
        """
        Create a delivery note from a confirmed purchase order
        """
        try:
            # Get the purchase order with items
            po_query = """
                SELECT po.*, 
                       t.name as tenant_name,
                       pr.requesting_tenant_id, pr.storeroom_id,
                       rt.name as requesting_tenant_name
                FROM purchase_orders po
                LEFT JOIN tenants t ON po.tenant_id = t.id
                LEFT JOIN purchase_requests pr ON po.purchase_request_id = pr.id
                LEFT JOIN tenants rt ON pr.requesting_tenant_id = rt.id
                WHERE po.id = ? AND po.status = 'confirmed'
            """
            
            po_results = db_manager.execute_query(po_query, (po_id,))
            if not po_results:
                raise Exception("Purchase order not found or not confirmed")
            
            purchase_order = po_results[0]
            
            # Get purchase order items
            items_query = """
                SELECT * FROM purchase_order_items 
                WHERE purchase_order_id = ?
                ORDER BY id
            """
            po_items = db_manager.execute_query(items_query, (po_id,))
            
            if not po_items:
                raise Exception("No items found in purchase order")
            
            # Generate delivery number
            delivery_number = self._generate_delivery_number(purchase_order['tenant_id'])
            
            # Create Delivery Note
            dn_data = {
                'delivery_number': delivery_number,
                'from_tenant_id': purchase_order['tenant_id'],  # Hub
                'to_tenant_id': purchase_order['requesting_tenant_id'],  # Requesting franchise
                'delivery_type': 'hub_to_franchise',
                'status': 'prepared',
                'delivery_date': delivery_data.get('delivery_date', datetime.now().date().isoformat()),
                'expected_delivery_date': delivery_data.get('expected_delivery_date', 
                                                          (datetime.now() + timedelta(days=2)).date().isoformat()),
                'delivery_address': delivery_data.get('delivery_address', purchase_order.get('delivery_address', '')),
                'total_amount': purchase_order['total_amount'],
                'notes': delivery_data.get('notes', f"Delivery for PO: {purchase_order['po_number']}"),
                'purchase_request_id': purchase_order['purchase_request_id'],
                'purchase_order_id': po_id,
                'created_by': user_id
            }
            
            # Insert Delivery Note
            dn_insert_query = """
                INSERT INTO delivery_notes (
                    delivery_number, from_tenant_id, to_tenant_id, delivery_type, status,
                    delivery_date, expected_delivery_date, delivery_address, total_amount,
                    notes, purchase_request_id, purchase_order_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            dn_params = (
                dn_data['delivery_number'], dn_data['from_tenant_id'], dn_data['to_tenant_id'],
                dn_data['delivery_type'], dn_data['status'], dn_data['delivery_date'],
                dn_data['expected_delivery_date'], dn_data['delivery_address'],
                dn_data['total_amount'], dn_data['notes'], dn_data['purchase_request_id'],
                dn_data['purchase_order_id'], dn_data['created_by']
            )
            
            dn_id = db_manager.execute_query(dn_insert_query, dn_params)
            
            # Insert Delivery Note Items
            for po_item in po_items:
                dn_item_query = """
                    INSERT INTO delivery_note_items (
                        delivery_note_id, item_name, description, quantity, unit,
                        unit_price, total_amount, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                dn_item_params = (
                    dn_id,
                    po_item['item_name'],
                    po_item['description'],
                    po_item['quantity'],
                    po_item['unit'],
                    po_item['unit_price'],
                    po_item['total_amount'],
                    po_item['notes']
                )
                
                db_manager.execute_query(dn_item_query, dn_item_params)
            
            self.logger.info(f"Delivery Note {delivery_number} created successfully from PO {purchase_order['po_number']}")
            
            return {
                'success': True,
                'dn_id': dn_id,
                'delivery_number': delivery_number,
                'total_amount': dn_data['total_amount'],
                'items_count': len(po_items)
            }
            
        except Exception as e:
            self.logger.error(f"Error creating delivery note from PO {po_id}: {e}")
            raise e
    
    def dispatch_delivery_note(self, dn_id, dispatch_data, user_id):
        """Mark delivery note as dispatched"""
        try:
            # Update delivery note status
            update_query = """
                UPDATE delivery_notes 
                SET status = 'dispatched', 
                    dispatched_at = CURRENT_TIMESTAMP,
                    dispatched_by = ?,
                    tracking_number = ?,
                    carrier = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND status = 'prepared'
            """
            
            params = (
                user_id,
                dispatch_data.get('tracking_number', ''),
                dispatch_data.get('carrier', ''),
                dn_id
            )
            
            result = db_manager.execute_query(update_query, params)
            
            if result:
                self.logger.info(f"Delivery Note {dn_id} dispatched successfully")
                return {'success': True, 'message': 'Delivery note dispatched successfully'}
            else:
                return {'success': False, 'error': 'Delivery note not found or not in prepared status'}
                
        except Exception as e:
            self.logger.error(f"Error dispatching delivery note {dn_id}: {e}")
            raise e
    
    def receive_delivery_note(self, dn_id, receipt_data, user_id):
        """
        Confirm delivery receipt and update inventory
        """
        try:
            # Get delivery note with items
            dn_query = """
                SELECT dn.*, 
                       pr.storeroom_id,
                       s.name as storeroom_name,
                       t.name as to_tenant_name
                FROM delivery_notes dn
                LEFT JOIN purchase_requests pr ON dn.purchase_request_id = pr.id
                LEFT JOIN storerooms s ON pr.storeroom_id = s.id
                LEFT JOIN tenants t ON dn.to_tenant_id = t.id
                WHERE dn.id = ? AND dn.status = 'dispatched'
            """
            
            dn_results = db_manager.execute_query(dn_query, (dn_id,))
            if not dn_results:
                raise Exception("Delivery note not found or not dispatched")
            
            delivery_note = dn_results[0]
            
            # Get delivery note items
            items_query = """
                SELECT * FROM delivery_note_items 
                WHERE delivery_note_id = ?
                ORDER BY id
            """
            dn_items = db_manager.execute_query(items_query, (dn_id,))
            
            if not dn_items:
                raise Exception("No items found in delivery note")
            
            # Update delivery note status
            update_dn_query = """
                UPDATE delivery_notes 
                SET status = 'delivered',
                    delivered_at = CURRENT_TIMESTAMP,
                    received_by = ?,
                    receiver_signature = ?,
                    delivery_notes = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            
            dn_update_params = (
                user_id,
                receipt_data.get('signature', ''),
                receipt_data.get('notes', ''),
                dn_id
            )
            
            db_manager.execute_query(update_dn_query, dn_update_params)
            
            # Update inventory for each item
            inventory_updates = []
            for item in dn_items:
                inventory_result = self._update_inventory_from_delivery(
                    item, delivery_note['storeroom_id'], delivery_note['to_tenant_id'], dn_id, user_id
                )
                inventory_updates.append(inventory_result)
            
            # Update related purchase order status
            if delivery_note['purchase_order_id']:
                po_update_query = """
                    UPDATE purchase_orders 
                    SET status = 'received', 
                        received_at = CURRENT_TIMESTAMP,
                        received_by = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                db_manager.execute_query(po_update_query, (user_id, delivery_note['purchase_order_id']))
            
            # Update related purchase request status
            if delivery_note['purchase_request_id']:
                pr_update_query = """
                    UPDATE purchase_requests 
                    SET status = 'completed', 
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                db_manager.execute_query(pr_update_query, (delivery_note['purchase_request_id'],))
            
            self.logger.info(f"Delivery Note {dn_id} received and inventory updated successfully")
            
            return {
                'success': True,
                'message': 'Delivery received and inventory updated successfully',
                'inventory_updates': inventory_updates
            }
            
        except Exception as e:
            self.logger.error(f"Error receiving delivery note {dn_id}: {e}")
            raise e
    
    def _update_inventory_from_delivery(self, delivery_item, storeroom_id, tenant_id, dn_id, user_id):
        """Update inventory when goods are received"""
        try:
            # Handle case where storeroom_id is None (fallback to tenant-based inventory)
            if storeroom_id is None:
                self.logger.warning(f"No storeroom_id provided for delivery {dn_id}, using tenant-based inventory")
                # Simplified query without storeroom_id and is_active columns
                inventory_query = """
                    SELECT * FROM inventory
                    WHERE (name = ? OR sku = ?)
                    AND tenant_id = ?
                    LIMIT 1
                """
                inventory_results = db_manager.execute_query(
                    inventory_query,
                    (delivery_item['item_name'], delivery_item['item_name'], tenant_id)
                )
            else:
                # Try the full query first, fall back to simplified if columns don't exist
                try:
                    inventory_query = """
                        SELECT * FROM inventory
                        WHERE (name = ? OR sku = ?)
                        AND storeroom_id = ?
                        AND tenant_id = ?
                        AND is_active = 1
                        LIMIT 1
                    """
                    inventory_results = db_manager.execute_query(
                        inventory_query,
                        (delivery_item['item_name'], delivery_item['item_name'], storeroom_id, tenant_id)
                    )
                except Exception as e:
                    if "no such column" in str(e):
                        self.logger.warning(f"Database schema missing columns, using simplified query: {e}")
                        # Fallback to basic query without missing columns
                        inventory_query = """
                            SELECT * FROM inventory
                            WHERE (name = ? OR sku = ?)
                            AND tenant_id = ?
                            LIMIT 1
                        """
                        inventory_results = db_manager.execute_query(
                            inventory_query,
                            (delivery_item['item_name'], delivery_item['item_name'], tenant_id)
                        )
                    else:
                        raise e
            
            if inventory_results:
                # Update existing inventory
                inventory_item = inventory_results[0]
                new_quantity = inventory_item['quantity'] + delivery_item['quantity']
                
                update_query = """
                    UPDATE inventory 
                    SET quantity = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """
                db_manager.execute_query(update_query, (new_quantity, inventory_item['id']))
                
                inventory_id = inventory_item['id']
                action = 'updated'
                
            else:
                # Create new inventory item
                # Generate SKU if not exists
                sku = f"AUTO-{delivery_item['item_name'][:10].upper().replace(' ', '')}-{datetime.now().strftime('%Y%m%d')}"

                # Try to insert with storeroom_id, fall back to basic insert if column doesn't exist
                try:
                    if storeroom_id is not None:
                        insert_query = """
                            INSERT INTO inventory (
                                name, sku, category, quantity, unit, cost_price,
                                storeroom_id, tenant_id, created_by
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        insert_params = (
                            delivery_item['item_name'],
                            sku,
                            'Reagents',  # Default category
                            delivery_item['quantity'],
                            'units',  # Default unit
                            delivery_item['unit_price'],
                            storeroom_id,
                            tenant_id,
                            user_id
                        )
                    else:
                        # Insert without storeroom_id
                        insert_query = """
                            INSERT INTO inventory (
                                name, sku, category, quantity, unit, cost_price,
                                tenant_id, created_by
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        insert_params = (
                            delivery_item['item_name'],
                            sku,
                            'Reagents',  # Default category
                            delivery_item['quantity'],
                            'units',  # Default unit
                            delivery_item['unit_price'],
                            tenant_id,
                            user_id
                        )
                except Exception as e:
                    if "no such column" in str(e):
                        self.logger.warning(f"Database schema missing columns, using basic insert: {e}")
                        # Fallback to basic insert with only essential columns
                        insert_query = """
                            INSERT INTO inventory (
                                name, sku, quantity, tenant_id, created_by
                            ) VALUES (?, ?, ?, ?, ?)
                        """
                        insert_params = (
                            delivery_item['item_name'],
                            sku,
                            delivery_item['quantity'],
                            tenant_id,
                            user_id
                        )
                    else:
                        raise e

                db_manager.execute_query(insert_query, insert_params)

                # Get the newly created inventory item ID
                inventory_id_query = "SELECT id FROM inventory WHERE name = ? AND tenant_id = ? ORDER BY id DESC LIMIT 1"
                inventory_id_result = db_manager.execute_query(inventory_id_query, (delivery_item['item_name'], tenant_id))
                inventory_id = inventory_id_result[0]['id'] if inventory_id_result else None

                action = 'created'
            
            # Create inventory transaction (if table exists)
            try:
                transaction_query = """
                    INSERT INTO inventory_transactions (
                        inventory_id, transaction_type, quantity, reference_type, reference_id,
                        reason, unit_cost, total_cost, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                db_manager.execute_query(transaction_query, (
                    inventory_id,
                    'in',
                    delivery_item['quantity'],
                    'delivery_note',
                    dn_id,
                    f"Goods received from delivery note",
                    delivery_item['unit_price'],
                    delivery_item.get('total_price', delivery_item['unit_price'] * delivery_item['quantity']),
                    user_id
                ))
            except Exception as e:
                if "no such table" in str(e) or "no such column" in str(e):
                    self.logger.warning(f"Inventory transactions table/column missing, skipping transaction log: {e}")
                else:
                    raise e
            
            return {
                'inventory_id': inventory_id,
                'item_name': delivery_item['item_name'],
                'quantity_added': delivery_item['quantity'],
                'action': action
            }
            
        except Exception as e:
            self.logger.error(f"Error updating inventory for item {delivery_item['item_name']}: {e}")
            raise e
    
    def _generate_delivery_number(self, tenant_id):
        """Generate a unique delivery number"""
        try:
            # Get tenant site code
            tenant_query = "SELECT site_code FROM tenants WHERE id = ?"
            tenant_result = db_manager.execute_query(tenant_query, (tenant_id,))
            site_code = tenant_result[0]['site_code'] if tenant_result else 'HUB'
            
            # Get next sequence number for today
            today = datetime.now().strftime('%Y%m%d')
            sequence_query = """
                SELECT COUNT(*) as count 
                FROM delivery_notes 
                WHERE delivery_number LIKE ? AND DATE(created_at) = DATE('now')
            """
            sequence_result = db_manager.execute_query(sequence_query, (f"DN-{site_code}-{today}-%",))
            sequence = (sequence_result[0]['count'] if sequence_result else 0) + 1
            
            return f"DN-{site_code}-{today}-{sequence:03d}"
            
        except Exception as e:
            self.logger.error(f"Error generating delivery number: {e}")
            return f"DN-{datetime.now().strftime('%Y%m%d%H%M%S')}"
