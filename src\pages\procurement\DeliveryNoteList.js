import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Badge, Alert } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTruck,
  faPlus,
  faSearch,
  faFilter,
  faEye,
  faCheck,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';
import ResponsiveDataTable from '../../components/common/ResponsiveDataTable';
import procurementAPI from '../../services/procurementAPI';

const DeliveryNoteList = () => {
  const [deliveryNotes, setDeliveryNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    delivery_type: '',
    start_date: '',
    end_date: ''
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  const navigate = useNavigate();

  useEffect(() => {
    loadDeliveryNotes();
  }, [filters, currentPage]);

  const loadDeliveryNotes = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        page: currentPage,
        limit: 20
      };
      
      if (searchQuery) {
        params.search = searchQuery;
      }

      const response = await procurementAPI.getDeliveryNotes(params);
      setDeliveryNotes(response.data.data || []);
      setTotalPages(response.data.pagination?.total_pages || 1);
    } catch (err) {
      console.error('Error loading delivery notes:', err);
      setError('Failed to load delivery notes');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    loadDeliveryNotes();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'prepared': return 'secondary';
      case 'dispatched': return 'warning';
      case 'in_transit': return 'info';
      case 'delivered': return 'success';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  };

  const getDeliveryTypeBadgeVariant = (type) => {
    switch (type) {
      case 'hub_to_franchise': return 'primary';
      case 'supplier_to_hub': return 'info';
      default: return 'secondary';
    }
  };

  const handleConfirmDelivery = async (deliveryId) => {
    try {
      await procurementAPI.confirmDelivery(deliveryId, {
        signature: 'Digital confirmation',
        delivery_notes: 'Confirmed via web interface'
      });
      loadDeliveryNotes();
    } catch (err) {
      console.error('Error confirming delivery:', err);
      setError('Failed to confirm delivery');
    }
  };

  const canConfirm = (delivery) => {
    return delivery.status === 'dispatched' && 
           (currentUser?.role === 'admin' || 
            currentUser?.role === 'hub_admin' || 
            delivery.to_tenant_id === currentUser?.tenant_id);
  };

  // Table columns
  const columns = [
    {
      key: 'delivery_number',
      label: 'Delivery #',
      render: (value, row) => (
        <Link to={`/procurement/deliveries/${row.id}`} className="text-decoration-none fw-semibold">
          {value}
        </Link>
      )
    },
    {
      key: 'delivery_date',
      label: 'Date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'delivery_type',
      label: 'Type',
      render: (value) => (
        <Badge bg={getDeliveryTypeBadgeVariant(value)} className="text-capitalize">
          {value?.replace('_', ' ')}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge bg={getStatusBadgeVariant(value)} className="text-capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'total_amount',
      label: 'Amount',
      render: (value) => `₹${parseFloat(value || 0).toLocaleString()}`
    },
    {
      key: 'expected_delivery_date',
      label: 'Expected',
      render: (value) => value ? new Date(value).toLocaleDateString() : '-'
    }
  ];

  // Mobile card configuration
  const mobileCardConfig = {
    title: (delivery) => delivery.delivery_number,
    subtitle: (delivery) => `${delivery.delivery_type?.replace('_', ' ')} • ₹${parseFloat(delivery.total_amount || 0).toLocaleString()}`,
    primaryField: 'delivery_date',
    secondaryField: 'expected_delivery_date',
    statusField: 'status'
  };

  const getRowActions = (delivery) => {
    const actions = [];

    actions.push({
      label: 'View',
      icon: faEye,
      variant: 'outline-primary',
      onClick: () => navigate(`/procurement/deliveries/${delivery.id}`)
    });

    if (canConfirm(delivery)) {
      actions.push({
        label: 'Confirm',
        icon: faCheck,
        variant: 'outline-success',
        onClick: () => handleConfirmDelivery(delivery.id)
      });
    }

    return actions;
  };

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
            Delivery Notes
          </h2>
          <p className="text-muted mb-0">Track and manage delivery notes</p>
        </div>
        {hasModuleAccess('DELIVERY_MANAGEMENT') && currentUser?.role === 'hub_admin' && (
          <Link to="/procurement/deliveries/create" className="btn btn-primary">
            <FontAwesomeIcon icon={faPlus} className="me-2" />
            New Delivery Note
          </Link>
        )}
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header className='text-primary'>
          <FontAwesomeIcon icon={faFilter} className="me-2" />
          Filters
        </Card.Header>
        <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
          <Form onSubmit={handleSearch}>
            <Row>
              <Col md={3} className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="prepared">Prepared</option>
                  <option value="dispatched">Dispatched</option>
                  <option value="in_transit">In Transit</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Delivery Type</Form.Label>
                <Form.Select
                  value={filters.delivery_type}
                  onChange={(e) => handleFilterChange('delivery_type', e.target.value)}
                >
                  <option value="">All Types</option>
                  <option value="hub_to_franchise">Hub to Franchise</option>
                  <option value="supplier_to_hub">Supplier to Hub</option>
                </Form.Select>
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => handleFilterChange('start_date', e.target.value)}
                />
              </Col>
              <Col md={3} className="mb-3">
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => handleFilterChange('end_date', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col md={6} className="mb-3">
                <Form.Label>Search</Form.Label>
                <div className="input-group">
                  <Form.Control
                    type="text"
                    placeholder="Search by delivery number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" variant="outline-secondary">
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Data Table */}
      <Card>
        <Card.Body className="p-0">
          <ResponsiveDataTable
            data={deliveryNotes}
            columns={columns}
            loading={loading}
            emptyMessage="No delivery notes found."
            mobileCardConfig={mobileCardConfig}
            getRowActions={getRowActions}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </Card.Body>
      </Card>
    </Container>
  );
};

export default DeliveryNoteList;
